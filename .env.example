# App Configuration
NODE_ENV=development
PORT=3000

# Database (Neon Postgres)
DATABASE_URL=****************************************/database

# Redis
REDIS_URL=redis://localhost:6379

# Supabase Auth
SUPABASE_PROJECT_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_JWT_SECRET=your-jwt-secret
SUPABASE_JWT_AUDIENCE=authenticated
SUPABASE_JWKS_URL=https://your-project.supabase.co/auth/v1/keys
SUPABASE_WEBHOOK_SECRET=your-webhook-secret

# Storage (S3 Compatible)
STORAGE_PROVIDER=aws
STORAGE_BUCKET=your-bucket-name
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1

# Email
EMAIL_PROVIDER=postmark
EMAIL_FROM=<EMAIL>
EMAIL_API_KEY=your-email-api-key

# Security
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# Features
ENABLE_SWAGGER=true
LOG_LEVEL=info
